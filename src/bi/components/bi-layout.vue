<script setup>
import { isEmpty } from 'lodash-es';
import { onMounted } from 'vue';
import HawkEditableName from '~/common/components/molecules/hawk-editable-name.vue';

const emit = defineEmits(['close']);

const state = reactive({
  is_renaming: false,
  widget_details: {},
});

function handleNameChange(name) {
  state.widget_details.name = name;
  state.is_renaming = false;
}

function closeEditor() {
  if (state.widget_details.name)
    state.is_renaming = false;
}

onMounted(() => {
  // NOTE: If we are about to edit some chart, we'll need to fetch the details from the store and assign it to state.widget_details
  if (isEmpty(state.widget_details))
    state.is_renaming = true;
});
</script>

<template>
  <HawkModalContainer content_class="h-full w-full rounded-none">
    <div class="col-span-12">
      <HawkModalHeader @close="emit('close')">
        <template #title>
          <div class="font-semibold text-lg h-9">
            <HawkEditableName
              v-if="state.is_renaming"
              class="max-w-[50vw]"
              :name="state.widget_details.name"
              placeholder="Enter name of the widget"
              :input_classes="{
                TextElement: {
                  input: 'font-semibold !text-xl !p-0',
                  inputContainer: 'border-0 border-b',
                },
                ElementLayout: {
                  innerWrapper: 'border-b',
                },
              }"
              @close="closeEditor"
              @update="handleNameChange($event)"
            />
            <div v-else class="group flex items-center gap-3 text-xl text-gray-900 font-semibold max-w-[65vw] mt-[3px]" @click="state.is_renaming = true">
              <span>
                {{ state.widget_details.name }}
              </span>
              <IconHawkEditOne class="w-4 h-4 group-hover:visible invisible cursor-pointer" @click="state.is_renaming = true" />
            </div>
          </div>
        </template>
      </HawkModalHeader>
      <HawkModalContent class="!p-0 max-h-[calc(100vh-85px)]">
        <div class="flex">
          <div class="w-1/4 py-6 px-4 h-[calc(100vh-85px)] border-r scrollbar">
            <slot name="left-content" />
          </div>
          <div class="h-[calc(100vh-85px)] w-3/4">
            <slot name="right-content" />
          </div>
        </div>
      </HawkModalContent>
    </div>
  </HawkModalContainer>
</template>
